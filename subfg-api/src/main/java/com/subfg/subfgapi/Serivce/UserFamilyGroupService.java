package com.subfg.subfgapi.Serivce;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.subfg.common.constans.FamilyGroupConstants;
import com.subfg.domain.entity.fg.FgFamilyGroupPo;
import com.subfg.domain.entity.fg.FgMemberPo;
import com.subfg.domain.vo.FamilyGroupVo;
import com.subfg.repository.mapper.FgFamilyGroupMapper;
import com.subfg.repository.mapper.FgMemberMapper;

import cn.dev33.satoken.stp.StpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户家庭组服务类
 * 处理用户个人相关的家庭组操作
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserFamilyGroupService {

    private final FgFamilyGroupMapper familyGroupMapper;
    private final FgMemberMapper fgMemberMapper;

    /**
     * 获取当前用户创建的家庭组列表
     */
    public List<FamilyGroupVo> getMyCreatedFamilyGroups() {
        String currentUserId = StpUtil.getLoginIdAsString();
        log.info("开始查询用户创建的家庭组列表，用户ID：{}", currentUserId);

        // 查询当前用户创建的所有家庭组
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FgFamilyGroupPo::getCreateUserId, currentUserId)
                   .orderByDesc(FgFamilyGroupPo::getCreateTime);

        List<FgFamilyGroupPo> familyGroups = familyGroupMapper.selectList(queryWrapper);

        // 转换为VO对象
        List<FamilyGroupVo> voList = familyGroups.stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());

        log.info("用户创建的家庭组列表查询完成，用户ID：{}，共查询到{}条记录", currentUserId, voList.size());
        return voList;
    }

    /**
     * 获取当前用户加入的家庭组列表（不包括自己创建的和团长是自己的）
     */
    public List<FamilyGroupVo> getMyJoinedFamilyGroups() {
        String currentUserId = StpUtil.getLoginIdAsString();
        log.info("开始查询用户加入的家庭组列表，用户ID：{}", currentUserId);

        // 先查询用户的成员记录
        List<FgMemberPo> memberList = fgMemberMapper.selectByUserId(currentUserId);

        // 获取家庭组ID列表
        List<String> familyGroupIds = memberList.stream()
                .map(FgMemberPo::getFamilyGroupId)
                .collect(Collectors.toList());

        if (familyGroupIds.isEmpty()) {
            log.info("用户未加入任何家庭组，用户ID：{}", currentUserId);
            return List.of();
        }

        // 查询家庭组信息，排除自己创建的和团长是自己的
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FgFamilyGroupPo::getFamilyGroupId, familyGroupIds)
                   .ne(FgFamilyGroupPo::getCreateUserId, currentUserId) // 排除自己创建的
                   .ne(FgFamilyGroupPo::getGroupLeaderId, currentUserId) // 排除团长是自己的
                   .orderByDesc(FgFamilyGroupPo::getCreateTime);

        List<FgFamilyGroupPo> familyGroups = familyGroupMapper.selectList(queryWrapper);

        // 转换为VO对象
        List<FamilyGroupVo> voList = familyGroups.stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());

        log.info("用户加入的家庭组列表查询完成，用户ID：{}，共查询到{}条记录", currentUserId, voList.size());
        return voList;
    }

    /**
     * 获取当前用户加入的拼团家庭组列表
     */
    public List<FamilyGroupVo> getMyJoinedGroupBuyingFamilyGroups() {
        String currentUserId = StpUtil.getLoginIdAsString();
        log.info("开始查询用户加入的拼团家庭组列表，用户ID：{}", currentUserId);

        // 先查询用户的成员记录
        List<FgMemberPo> memberList = fgMemberMapper.selectByUserId(currentUserId);

        // 获取家庭组ID列表
        List<String> familyGroupIds = memberList.stream()
                .map(FgMemberPo::getFamilyGroupId)
                .collect(Collectors.toList());

        if (familyGroupIds.isEmpty()) {
            log.info("用户未加入任何家庭组，用户ID：{}", currentUserId);
            return List.of();
        }

        // 查询拼团家庭组信息，状态为组建中
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FgFamilyGroupPo::getFamilyGroupId, familyGroupIds)
                   .eq(FgFamilyGroupPo::getGroupType, FamilyGroupConstants.GroupType.GROUP_BUYING) // 只查询拼团家庭组
                   .eq(FgFamilyGroupPo::getFamilyGroupStatus, FamilyGroupConstants.Status.BUILDING) // 状态为组建中
                   .orderByDesc(FgFamilyGroupPo::getCreateTime);

        List<FgFamilyGroupPo> familyGroups = familyGroupMapper.selectList(queryWrapper);

        // 转换为VO对象
        List<FamilyGroupVo> voList = familyGroups.stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());

        log.info("用户加入的拼团家庭组列表查询完成，用户ID：{}，共查询到{}条记录", currentUserId, voList.size());
        return voList;
    }

    /**
     * 获取当前用户为团长的家庭组列表
     */
    public List<FamilyGroupVo> getMyFamilyGroupsByLeader() {
        String currentUserId = StpUtil.getLoginIdAsString();
        log.info("开始查询用户为团长的家庭组列表，用户ID：{}", currentUserId);

        // 查询当前用户为团长的所有家庭组
        LambdaQueryWrapper<FgFamilyGroupPo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FgFamilyGroupPo::getGroupLeaderId, currentUserId)
                   .orderByDesc(FgFamilyGroupPo::getCreateTime);

        List<FgFamilyGroupPo> familyGroups = familyGroupMapper.selectList(queryWrapper);

        // 转换为VO对象
        List<FamilyGroupVo> voList = familyGroups.stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());

        log.info("用户为团长的家庭组列表查询完成，用户ID：{}，共查询到{}条记录", currentUserId, voList.size());
        return voList;
    }

    /**
     * 将实体对象转换为VO对象
     */
    private FamilyGroupVo convertToVo(FgFamilyGroupPo po) {
        FamilyGroupVo vo = new FamilyGroupVo();
        vo.setFamilyGroupId(po.getFamilyGroupId());
        vo.setFamilyGroupName(po.getFamilyGroupName());
        vo.setDescription(po.getDescription());
        vo.setGroupType(po.getGroupType());
        vo.setGroupTypeName(getGroupTypeName(po.getGroupType()));
        vo.setFamilyGroupStatus(po.getFamilyGroupStatus());
        vo.setStatusName(getStatusName(po.getFamilyGroupStatus()));
        vo.setProductId(po.getProductId());
        vo.setRegionId(po.getRegionId());
        vo.setPlanId(po.getPlanId());
        vo.setAmount(po.getAmount());
        vo.setBillingCycle(po.getBillingCycle());
        vo.setCurrentMemberCount(po.getCurrentMemberCount());
        vo.setSumVacancy(po.getSumVacancy());
        vo.setDeadline(po.getDeadline());
        vo.setGroupLeaderId(po.getGroupLeaderId());
        vo.setCreateUserId(po.getCreateUserId());
        vo.setCreateTime(po.getCreateTime());
        vo.setUpdateTime(po.getUpdateTime());
        vo.setLatestJoinTime(po.getLatestJoinTime());
        vo.setLaunchTime(po.getLaunchTime());
        vo.setIsConverted(po.getIsConverted());
        return vo;
    }

    /**
     * 获取家庭组类型名称
     */
    private String getGroupTypeName(Integer groupType) {
        if (groupType == null) {
            return null;
        }
        switch (groupType) {
            case FamilyGroupConstants.GroupType.SELF_BUILT:
                return "自建家庭组";
            case FamilyGroupConstants.GroupType.GROUP_BUYING:
                return "拼团家庭组";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取家庭组状态名称
     */
    private String getStatusName(Integer status) {
        if (status == null) {
            return null;
        }
        switch (status) {
            case FamilyGroupConstants.Status.REVIEWING:
                return "审核中";
            case FamilyGroupConstants.Status.BUILDING:
                return "组建中";
            case FamilyGroupConstants.Status.LAUNCHED:
                return "已发车";
            case FamilyGroupConstants.Status.CLOSED:
                return "已关闭";
            case FamilyGroupConstants.Status.REVIEW_REJECTED:
                return "审核未通过";
            default:
                return "未知状态";
        }
    }
}
