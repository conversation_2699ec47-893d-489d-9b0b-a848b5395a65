package com.subfg.subfgapi;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = "com.subfg")
@MapperScan("com.subfg.repository.mapper")
@EnableRabbit
@EnableAsync
public class SubfgApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(SubfgApiApplication.class, args);
    }

}
