package com.subfg.domain.entity.fg;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 家庭组成员实体类
 * 对应数据库表：fg_member
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("fg_member")
public class FgMemberPo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成员ID（主键）
     */
    @TableId("member_id")
    private String memberId;

    /**
     * 家庭组ID
     */
    @TableField("family_group_id")
    private String familyGroupId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 邀请时间
     */
    @TableField("invitation_time")
    private Long invitationTime;

    /**
     * 激活时间（团长确认时间）
     */
    @TableField("active_time")
    private Long activeTime;

    /**
     * 服务开始时间
     */
    @TableField("service_start_time")
    private Long serviceStartTime;

    /**
     * 服务结束时间
     */
    @TableField("service_over_time")
    private Long serviceOverTime;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Long updateTime;

}
